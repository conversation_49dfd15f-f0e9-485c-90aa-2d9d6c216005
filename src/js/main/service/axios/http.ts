import Http from './axios'
import { storage } from '../../utils/storage'
import { useAuthStore } from '../../store/useAuthStore'
import CSInterface from '../../../lib/cep/csinterface'
import { VERSION } from '../../config'

export default new Http({
  // baseURL: 'https://pylon-log1.igame.163.com/dubhe/api/ae/plugin',
  baseURL: '/dubhe/api/plugin',
  timeout: 10000,
  interceptors: {
    requestInterceptor(config) {
      const token = useAuthStore.getState().token || storage.get('token')
      const cs = new CSInterface()
      const osInfo = cs.getOSInformation()
      const osSystem = osInfo.indexOf('Mac OS X') > -1 ? 'Mac' : osInfo.indexOf('Windows') > -1 ? 'Windows' : ''
      const env = window?.__adobe_cep__?.getHostEnvironment()
      const aeVersion = env?.appVersion || '24.0.0'

      config.headers!['X-Auth-Token'] = token         // 用户认证 token
      config.headers!['X-System'] = osSystem          // 操作系统类型（Mac/Windows）
      config.headers!['X-AE-Version'] = aeVersion     // Photoshop 版本号
      config.headers!['X-Version'] = VERSION          // 插件版本号
      return config
    }
  }
})
