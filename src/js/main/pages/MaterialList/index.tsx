import { Button, Input } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { SearchOutlined } from '@ant-design/icons'
import { fetchMaterialList } from '../../service/material'
import { ProjectSwitcher } from './components/ProjectSwitcher'
import { ImportButton } from './components/ImportButton'
import { SaveButton } from './components/SaveButton'
import { VirtualList } from '../../components/VirtualList'
import { MaterialItem } from './components/MaterialItem'

const MaterialList: React.FC = () => {
  const [curProjectId, setCurProjectId] = useState('-3')
  const [materialList, setMaterialList] = useState<Record<string, any>[]>([])
  const [keyword, setKeyword] = useState('')

  useEffect(() => {
    getMaterialList()
  }, [curProjectId, keyword])

  const getMaterialList = async () => {
    const params = {
      limit: 120,
      keyword,
      projectId: curProjectId,
      sort: '', // 排序规则
      reqTime: 'ALL', // 时间范围所有
      lastId: materialList[materialList.length - 1]?.id || null,
      lastScore: materialList[materialList.length - 1]?.score || null
    }
    // const res = await fetchMaterialList(params)
    // console.log(1111, 'res', res)
    setMaterialList([
      { id: '1', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '2', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '3', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '4', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '5', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '6', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '7', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '8', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '9', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' },
      { id: '10', thumbnailUrl: 'https://skyhub-private.nos-jd.163yun.com/1b2a7b890dcd7bc4d205b990e80af0ec.JPG' }
    ])
  }

  return (
    <Page>
      <Header>
        <ProjectSwitcher curProjectId={curProjectId} setCurProjectId={setCurProjectId} />

        <Input
          allowClear
          onClear={() => setKeyword('')}
          prefix={<SearchOutlined />}
          placeholder="搜索你想要的素材"
          style={{ marginLeft: 8 }}
        />

        <ImportButton />

        <SaveButton />
      </Header>

      <Content>
        {/*
              reqTimeOptions: [
        {
          id: 'DEFAULT',
          name: '综合排序'
        },
        {
          id: 'MOST_FREQUENTLY_DOWNLOAD',
          name: '最常下载'
        },
        {
          id: 'RECENTLY_DOWNLOAD',
          name: '最近下载'
        },
        {
          id: 'UPLOAD_TIME_ASC',
          name: '按时间排序'
        },
        {
          id: 'UPLOAD_TIME_DESC',
          name: '按时间倒排序'
        }
      ],
        */}
        <VirtualList
          height={0}
          data={materialList}
          itemKey="id"
          loadMore={getMaterialList}
          renderItem={(item, itemWidth) => <MaterialItem item={item} itemWidth={itemWidth} />}
          direction="horizontal"
          gap={16}
          minItemWidth={120}
          maxItemWidth={300}
        />
      </Content>
    </Page>
  )
}

const Page = styled.div`
    display: flex;
    flex-direction: column;
    min-height: 97vh;
    padding: 8px;
    background-color: #494949;
`

const Header = styled.div`
    display: flex;
    align-items: center;
`

const Content = styled.div`
    flex: 1;
    margin-top: 8px;
    border: 1px solid red;
    overflow: hidden;
`

export default MaterialList
