import React, { forwardRef } from 'react'
import styled from 'styled-components'

interface MaterialItemProps {
  item: Record<string, any>
}

export const MaterialItem = forwardRef<HTMLDivElement, MaterialItemProps>(
  ({ item }, ref) => {
    return (
      <Container ref={ref}>
        {/*<ItemImage src={item.thumbnailUrl} alt="" />*/}
      </Container>
    )
  }
)

const Container = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 159px;
    height: 110px;
    border: 1px solid #3E3E3E;
    border-radius: 8px;
    cursor: pointer;
    background-color: red;
`

// const ItemImage = styled.img`
//     width: 100px;
// `
