import React, { useEffect, useMemo, useState } from 'react'
import { Button, Dropdown } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import type { MenuInfo } from 'rc-menu/lib/interface'

interface ProjectSwitcherProps {
  curProjectId: string
  setCurProjectId: (projectId: string) => void
}

export const ProjectSwitcher: React.FC<ProjectSwitcherProps> = ({ curProjectId, setCurProjectId }) => {
  const [projects, setProjects] = useState<{ label: string; key: string }[]>([])

  const curProjectLabel = useMemo(() => {
    return projects.find(p => p.key === curProjectId)?.label
  }, [projects, curProjectId])

  useEffect(() => {
    setProjects([{ label: '公共素材', key: '-3' }])
  }, [])

  const handleChangeProject = (e: MenuInfo) => {
    setCurProjectId(e.key)
  }

  return (
    <Dropdown
      overlayStyle={{ width: 180 }}
      trigger={['click']}
      menu={{
        items: projects,
        onClick: handleChangeProject
      }}>
      <Button>
        <div style={{ width: 154, textAlign: 'left' }}>{curProjectLabel}</div>
        <DownOutlined />
      </Button>
    </Dropdown>
  )
}