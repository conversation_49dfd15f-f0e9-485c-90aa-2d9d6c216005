import React, { useEffect, useState, useRef, useCallback } from 'react'
import { List, message } from 'antd'
import RcVirtualList from 'rc-virtual-list'
import styled from 'styled-components'

interface VirtualListProps {
  height: number;
  data: Record<string, any>[];
  itemKey: string;
  loadMore: () => Promise<void>;
  renderItem: (item: Record<string, any>, itemWidth?: number) => React.ReactNode;
  // 新增横向布局相关配置
  direction?: 'vertical' | 'horizontal';
  itemWidth?: number; // 单个item的宽度
  gap?: number; // item之间的间距
  minItemWidth?: number; // 最小item宽度，用于自适应
  maxItemWidth?: number; // 最大item宽度，用于自适应
}

export const VirtualList: React.FC<VirtualListProps> = ({
  height = 400,
  data,
  itemKey,
  loadMore,
  renderItem,
  direction = 'vertical',
  itemWidth,
  gap = 16,
  minItemWidth = 120,
  maxItemWidth = 300
}) => {
  const [loading, setLoading] = useState(false)
  const [containerWidth, setContainerWidth] = useState(0)
  const [calculatedItemWidth, setCalculatedItemWidth] = useState(itemWidth || minItemWidth)
  const [itemsPerRow, setItemsPerRow] = useState(3)
  const containerRef = useRef<HTMLDivElement>(null)

  // 计算自适应宽度和间距
  const calculateLayout = useCallback(() => {
    if (!containerRef.current || direction === 'vertical') return

    const containerW = containerRef.current.offsetWidth
    setContainerWidth(containerW)

    if (itemWidth) {
      // 如果指定了固定宽度，直接使用
      setCalculatedItemWidth(itemWidth)
      return
    }

    // 自适应计算：根据容器宽度计算最佳item宽度和每行item数量
    // 假设每行至少显示3个item，最多显示8个item
    const minItemsPerRow = 3
    const maxItemsPerRow = 8

    let bestLayout = {
      itemWidth: minItemWidth,
      itemsPerRow: minItemsPerRow
    }

    // 尝试不同的每行item数量，找到最佳布局
    for (let itemsPerRow = minItemsPerRow; itemsPerRow <= maxItemsPerRow; itemsPerRow++) {
      // 计算在这个item数量下，每个item的最大可能宽度
      // 容器宽度 = itemsPerRow * itemWidth + (itemsPerRow - 1) * gap + 2 * padding
      // 解出 itemWidth = (containerW - 2 * gap - (itemsPerRow - 1) * gap) / itemsPerRow
      const calculatedWidth = (containerW - 2 * gap - (itemsPerRow - 1) * gap) / itemsPerRow

      if (calculatedWidth >= minItemWidth && calculatedWidth <= maxItemWidth) {
        bestLayout = {
          itemWidth: calculatedWidth,
          itemsPerRow: itemsPerRow
        }
      }
    }

    setCalculatedItemWidth(Math.floor(bestLayout.itemWidth))
    setItemsPerRow(bestLayout.itemsPerRow)
  }, [containerWidth, itemWidth, gap, minItemWidth, maxItemWidth, direction])

  // 监听容器宽度变化
  useEffect(() => {
    calculateLayout()

    const resizeObserver = new ResizeObserver(() => {
      calculateLayout()
    })

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [calculateLayout])

  const appendData = async () => {
    try {
      setLoading(true)
      await loadMore()
    } catch (error) {
      message.error(`加载列表失败, ${(error as Error).message}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    appendData()
  }, [])

  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (direction === 'horizontal') {
      // 横向滚动检测
      if (Math.abs(e.currentTarget.scrollWidth - e.currentTarget.scrollLeft - e.currentTarget.clientWidth) <= 1) {
        appendData()
      }
    } else {
      // 垂直滚动检测
      if (Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - height) <= 1) {
        appendData()
      }
    }
  }

  if (direction === 'horizontal') {
    return (
      <HorizontalContainer ref={containerRef}>
        <HorizontalList gap={gap} itemsPerRow={itemsPerRow}>
          {data.map((item, index) => (
            <HorizontalItem key={item[itemKey]} gap={gap} itemWidth={calculatedItemWidth}>
              {renderItem(item, calculatedItemWidth)}
            </HorizontalItem>
          ))}
        </HorizontalList>
      </HorizontalContainer>
    )
  }

  return (
    <List>
      <RcVirtualList
        data={data}
        height={height}
        itemKey={itemKey}
        onScroll={onScroll}
      >
        {(item: Record<string, any>) => renderItem(item)}
      </RcVirtualList>
    </List>
  )
}

// 横向布局的样式组件
const HorizontalContainer = styled.div`
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
`

const HorizontalList = styled.div<{ gap: number; itemsPerRow: number }>`
  display: grid;
  grid-template-columns: repeat(${props => props.itemsPerRow}, 1fr);
  gap: ${props => props.gap}px;
  padding: ${props => props.gap}px;
  align-content: flex-start;
`

const HorizontalItem = styled.div<{ gap: number; itemWidth: number }>`
  width: ${props => props.itemWidth}px;
  justify-self: center;
`