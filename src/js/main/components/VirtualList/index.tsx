import React, { useEffect, useState } from 'react'
import { List, message } from 'antd'
import RcVirtualList from 'rc-virtual-list'

interface VirtualListProps {
  height: number;
  data: Record<string, any>[];
  itemKey: string;
  loadMore: () => Promise<void>;
  renderItem: (item: Record<string, any>) => React.ReactNode;
}

export const VirtualList: React.FC<VirtualListProps> = ({
  height = 400,
  data,
  itemKey,
  loadMore,
  renderItem
}) => {
  const [loading, setLoading] = useState(false)

  const appendData = async () => {
    try {
      setLoading(true)
      await loadMore()
    } catch (error) {
      message.error(`加载列表失败, ${(error as Error).message}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    appendData()
  }, [])

  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - height) <= 1) {
      appendData()
    }
  }

  return (
    <List>
      <RcVirtualList
        data={data}
        height={height}
        itemKey={itemKey}
        onScroll={onScroll}
      >
        {(item: Record<string, any>) => renderItem(item)}
      </RcVirtualList>
    </List>
  )
}